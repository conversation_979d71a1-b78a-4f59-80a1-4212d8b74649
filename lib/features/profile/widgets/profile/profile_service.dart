import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/chat/logic/chat/chat_cubit.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/features/chat/screens/conversation_screen.dart';

import 'package:gp_stock_app/shared/widgets/alert_dilaog/custom_alert_dialog.dart';

import '../../../../core/utils/icon_helper.dart';
import '../../../../core/utils/utils.dart';
import '../../../../shared/constants/assets.dart';

import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';

class ProfileServiceConsult extends StatefulWidget {
  const ProfileServiceConsult({super.key});

  @override
  State<ProfileServiceConsult> createState() => _ProfileServiceConsultState();
}

class _ProfileServiceConsultState extends State<ProfileServiceConsult> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );
    _scaleAnimation = Tween<double>(begin: 0.9, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Container(
          height: 74.gh,
          width: 347.gw,
          decoration: BoxDecoration(
            color: context.theme.cardColor,
            borderRadius: BorderRadius.circular(8.gr),
          ),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.gw),
            child: Row(
              children: [
                IconButton(
                    iconSize: 40.gr,
                    icon: IconHelper.loadAsset(
                      Assets.chatServiceIcon,
                      width: 40.gw,
                      height: 40.gh,
                      shouldEnableThemeGradient: true,
                      colors: [context.theme.primaryColorLight, context.theme.primaryColor],
                    ),
                    onPressed: null),
                16.horizontalSpace,
                Expanded(
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'exclusiveService'.tr(),
                        style: context.textTheme.primary,
                      ),
                      4.horizontalSpace,
                      CommonButton(
                        title: 'consultNow'.tr(),
                        width: 80.gw,
                        height: 30.gh,
                        fontSize: 12.gsp,
                        radius: 5.gr,
                        onPressed: () => context.verifyAuth(() async {
                          final conversation = await context.read<ChatCubit>().getChatServicesAccount();
                          if (conversation != null) {
                            if (mounted && conversation.pImAccount.isEmpty) {
                              _handleNoHaveHeadAccount();
                              return;
                            }
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) => ConversationScreen(conversation: conversation)));
                          }
                        }),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _handleNoHaveHeadAccount() {
    Helper.showDialogPopUp(
      context,
      CustomAlertDialog(
        message: 'noExclusiveSupport'.tr(),
        actionButtonText: 'ok'.tr(),
        buttonBackGroundColor: context.theme.primaryColor,
        onActionButtonPressed: () {
          Navigator.pop(context);
        },
        isLoading: false,
        messageTextStyle: context.textTheme.primary.fs20.w600,
      ),
      dialogKey: "noExclusiveSupport",
      barrierDismissible: true,
    );
  }
}
