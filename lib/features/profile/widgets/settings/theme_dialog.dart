import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/logic/theme/theme_cubit.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';

import '../../../../shared/widgets/alert_dilaog/dialog_helper.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class ThemeDialog extends StatelessWidget {
  const ThemeDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, state) {
        final themeMode = state.themeMode;
        return Padding(
          padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Container(
            height: 320.gh,
            decoration: BoxDecoration(
              color: context.theme.cardColor,
              borderRadius: BorderRadius.circular(20.gr),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                DialogHeader(title: 'theme'.tr()),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 8.gh),
                  child: Column(
                    children: [
                      ListTile(
                        onTap: () {
                          HapticFeedback.lightImpact();
                          context.read<ThemeCubit>().changeTheme(ThemeMode.system);
                          Navigator.pop(context);
                        },
                        contentPadding: EdgeInsets.symmetric(horizontal: 8.gw),
                        leading: Icon(
                          Icons.settings,
                          color: context.theme.primaryColor,
                          size: 24.gr,
                        ),
                        title: Text(
                          'systemTheme'.tr(),
                          style: context.textTheme.primary.w500,
                        ),
                        trailing: themeMode == ThemeMode.system
                            ? Icon(
                                Icons.check_circle,
                                color: context.theme.primaryColor,
                                size: 20.gr,
                              )
                            : null,
                        selected: themeMode == ThemeMode.system,
                        selectedTileColor: context.theme.primaryColor.withValues(alpha: 0.1),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.gr),
                        ),
                      ),
                      8.verticalSpace,
                      ListTile(
                        onTap: () {
                          HapticFeedback.lightImpact();
                          context.read<ThemeCubit>().changeTheme(ThemeMode.light);
                          Navigator.pop(context);
                        },
                        contentPadding: EdgeInsets.symmetric(horizontal: 8.gw),
                        leading: Icon(
                          Icons.wb_sunny,
                          color: context.theme.primaryColor,
                          size: 24.gr,
                        ),
                        title: Text(
                          'lightTheme'.tr(),
                          style: context.textTheme.primary.w500,
                        ),
                        trailing: themeMode == ThemeMode.light
                            ? Icon(
                                Icons.check_circle,
                                color: context.theme.primaryColor,
                                size: 20.gr,
                              )
                            : null,
                        selected: themeMode == ThemeMode.light,
                        selectedTileColor: context.theme.primaryColor.withValues(alpha: 0.1),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.gr),
                        ),
                      ),
                      8.verticalSpace,
                      ListTile(
                        onTap: () {
                          HapticFeedback.lightImpact();
                          context.read<ThemeCubit>().changeTheme(ThemeMode.dark);
                          Navigator.pop(context);
                        },
                        contentPadding: EdgeInsets.symmetric(horizontal: 8.gw),
                        leading: Icon(
                          Icons.nights_stay,
                          color: context.theme.primaryColor,
                          size: 24.gr,
                        ),
                        title: Text(
                          'darkTheme'.tr(),
                          style: context.textTheme.primary.w500,
                        ),
                        trailing: themeMode == ThemeMode.dark
                            ? Icon(
                                Icons.check_circle,
                                color: context.theme.primaryColor,
                                size: 20.gr,
                              )
                            : null,
                        selected: themeMode == ThemeMode.dark,
                        selectedTileColor: context.theme.primaryColor.withValues(alpha: 0.1),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.gr),
                        ),
                      ),
                      DialogFooter(),
                      16.verticalSpace,
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
