{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    // GP Flavor
    {
      "name": "GP (debug)",
      "request": "launch",
      "type": "dart",
      "flutterMode": "debug",
      "program": "lib/main_gp.dart",
      "preLaunchTask": "Switch Flavor: GP",
      "args": [
        "--flavor",
        "gp",
        "-t",
        "lib/main_gp.dart"
      ],
      // Add toolArgs if needed
      // "toolArgs": [
      //   "--dart-define",
      //   "MY_VAR=MY_VALUE",
      //   "--dart-define",
      //   "MY_OTHER_VAR=MY_OTHER_VALUE"
      // ]
    },
    {
      "name": "GP (debug) goldenA",
      "request": "launch",
      "type": "dart",
      "flutterMode": "debug",
      "program": "lib/main_gp.dart",
      "preLaunchTask": "Switch Flavor: goldenA",
      "args": [
        "--flavor",
        "gp",
        "-t",
        "lib/main_gp.dart"
      ],
      // Add toolArgs if needed
      // "toolArgs": [
      //   "--dart-define",
      //   "MY_VAR=MY_VALUE",
      //   "--dart-define",
      //   "MY_OTHER_VAR=MY_OTHER_VALUE"
      // ]
    },
    {
      "name": "GP (profile)",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile",
      "program": "lib/main_gp.dart",
      "preLaunchTask": "Switch Flavor: GP",
      "args": [
        "--flavor",
        "gp",
        "-t",
        "lib/main_gp.dart"
      ]
    },
    {
      "name": "GP (release)",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release",
      "program": "lib/main_gp.dart",
      "preLaunchTask": "Switch Flavor: GP",
      "args": [
        "--flavor",
        "gp",
        "-t",
        "lib/main_gp.dart"
      ]
    },
    // RSYP Flavor
    {
      "name": "RSYP (debug)",
      "request": "launch",
      "type": "dart",
      "flutterMode": "debug",
      "program": "lib/main_rsyp.dart",
      "preLaunchTask": "Switch Flavor: RSYP",
      "args": [
        "--flavor",
        "rsyp",
        "-t",
        "lib/main_rsyp.dart"
      ]
    },
    {
      "name": "RSYP (profile)",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile",
      "program": "lib/main_rsyp.dart",
      "preLaunchTask": "Switch Flavor: RSYP",
      "args": [
        "--flavor",
        "rsyp",
        "-t",
        "lib/main_rsyp.dart"
      ]
    },
    {
      "name": "RSYP (release)",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release",
      "program": "lib/main_rsyp.dart",
      "preLaunchTask": "Switch Flavor: RSYP",
      "args": [
        "--flavor",
        "rsyp",
        "-t",
        "lib/main_rsyp.dart"
      ]
    },
    // Pre Flavor
    {
      "name": "Pre (debug)",
      "request": "launch",
      "type": "dart",
      "flutterMode": "debug",
      "program": "lib/main_pre.dart",
      "preLaunchTask": "Switch Flavor: Pre",
      "args": [
        "--flavor",
        "pre",
        "-t",
        "lib/main_pre.dart"
      ]
    },
    {
      "name": "Pre (profile)",
      "request": "launch",
      "type": "dart",
      "flutterMode": "profile",
      "program": "lib/main_pre.dart",
      "preLaunchTask": "Switch Flavor: Pre",
      "args": [
        "--flavor",
        "pre",
        "-t",
        "lib/main_pre.dart"
      ]
    },
    {
      "name": "Pre (release)",
      "request": "launch",
      "type": "dart",
      "flutterMode": "release",
      "program": "lib/main_pre.dart",
      "preLaunchTask": "Switch Flavor: Pre",
      "args": [
        "--flavor",
        "pre",
        "-t",
        "lib/main_pre.dart"
      ]
    },
    {
      "name": "YHXT (debug)",
      "request": "launch",
      "type": "dart",
      "flutterMode": "debug",
      "program": "lib/main_yhxt.dart",
      "preLaunchTask": "Switch Flavor: YHXT",
      "args": [
        "--flavor",
        "yhxt",
        "-t",
        "lib/main_yhxt.dart"
      ]
    }
  ]
}